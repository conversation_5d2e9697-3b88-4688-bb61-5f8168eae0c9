"""
[二次开发] 统一Again功能管理器

提供跨工作流的--again功能支持，包括：
- 统一的工作流数据保存和加载
- 支持AIGEN、KONTEXT、KONTEXT_API等所有工作流类型
- 用户隔离和数据过期管理
- 简洁优雅的API接口

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：统一的--again功能管理
- 维护者：开发团队
- 最后更新：2025-01-11
- 相关任务：again功能重构
"""

import logging
import random
import copy
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from datetime import datetime, timedelta
from pkg.workers.flux.workflow_persistence_manager import WorkflowPersistenceManager, WorkflowSnapshot
from pkg.core.workflow.manager_base import WorkflowResult


@dataclass
class AgainRequest:
    """Again请求数据类"""
    user_id: str
    chat_id: str = ""
    workflow_type: str = "aigen"  # aigen, kontext, kontext_api


class UnifiedAgainManager:
    """统一的Again功能管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.persistence_manager = WorkflowPersistenceManager()
        
        # 支持的工作流类型
        self.supported_workflows = {
            'aigen': 'FluxWorkflowManager',
            'kontext': 'LocalKontextWorkflowManager', 
            'kontext_api': 'KontextAPIManager'
        }
    
    async def handle_again_request(self, request: AgainRequest) -> WorkflowResult:
        """
        处理统一的--again请求
        
        Args:
            request: Again请求对象
            
        Returns:
            WorkflowResult: 执行结果
        """
        try:
            self.logger.info(f"🔄 处理统一--again请求 - 用户: {request.user_id}, 类型: {request.workflow_type}")

            # 1. 加载上一次的工作流数据
            self.logger.info(f"🔍 [DEBUG] 尝试加载工作流数据 - 用户: {request.user_id}, 聊天: {request.chat_id}")

            # 🔥 新增：详细的调试信息
            expected_file = self.persistence_manager._get_user_workflow_file(request.user_id)
            self.logger.info(f"🔍 [DEBUG] 期望的文件路径: {expected_file}")
            self.logger.info(f"🔍 [DEBUG] 文件是否存在: {expected_file.exists()}")

            snapshot = self.persistence_manager.load_last_successful_workflow(
                request.user_id,
                request.chat_id
            )

            if not snapshot:
                self.logger.warning(f"⚠️ [DEBUG] 没有找到工作流数据 - 用户: {request.user_id}")
                self.logger.warning(f"⚠️ [DEBUG] 请检查用户ID格式是否正确")

                # 🔥 新增：列出当前存在的工作流文件
                try:
                    temp_dir = self.persistence_manager.storage_dir
                    existing_files = list(temp_dir.glob("last_workflow_*.json"))
                    self.logger.info(f"🔍 [DEBUG] 当前存在的工作流文件:")
                    for file in existing_files:
                        self.logger.info(f"   📄 {file.name}")
                except Exception as e:
                    self.logger.error(f"❌ 列出现有文件失败: {e}")

                return WorkflowResult(
                    success=False,
                    error_message="没有找到上一次的工作流数据，请先执行一次正常的图片生成"
                )

            self.logger.info(f"✅ [DEBUG] 找到工作流数据 - 类型: {snapshot.workflow_type}, 时间: {snapshot.timestamp}")
            
            # 2. 验证工作流类型匹配
            self.logger.info(f"🔍 [DEBUG] 验证工作流类型 - 快照类型: {snapshot.workflow_type}, 请求类型: {request.workflow_type}")
            if not self._validate_workflow_type(snapshot, request.workflow_type):
                self.logger.warning(f"⚠️ [DEBUG] 工作流类型不匹配 - 快照: {snapshot.workflow_type}, 请求: {request.workflow_type}")
                return WorkflowResult(
                    success=False,
                    error_message=f"上一次的工作流类型({snapshot.workflow_type})与当前请求类型({request.workflow_type})不匹配"
                )
            
            self.logger.info(f"✅ 找到匹配的工作流 - 类型: {snapshot.workflow_type}, 提示词: {snapshot.user_prompt[:50]}...")
            
            # 3. 根据工作流类型选择执行器
            self.logger.info(f"🔍 [DEBUG] 开始执行工作流 - 类型: {snapshot.workflow_type}")

            # 🔥 增强调试：记录快照详细信息
            self.logger.info(f"🔍 [DEBUG] 快照详情:")
            self.logger.info(f"  - 工作流类型: {snapshot.workflow_type}")
            self.logger.info(f"  - 用户提示词: {snapshot.user_prompt[:100]}...")
            self.logger.info(f"  - 优化提示词: {snapshot.optimized_prompt[:100]}...")
            self.logger.info(f"  - 工作流数据大小: {len(snapshot.workflow_data) if snapshot.workflow_data else 0} 节点")
            self.logger.info(f"  - 参数: {snapshot.parameters}")
            self.logger.info(f"  - 图片信息: {snapshot.image_info}")

            result = await self._execute_workflow_by_type(snapshot, request)

            # 🔥 增强调试：记录执行结果详细信息
            self.logger.info(f"🔍 [DEBUG] 执行结果详情:")
            self.logger.info(f"  - 成功: {result.success}")
            self.logger.info(f"  - 错误信息: {result.error_message if not result.success else 'N/A'}")
            self.logger.info(f"  - 元数据: {result.metadata}")
            if result.success and hasattr(result, 'image_data'):
                self.logger.info(f"  - 图片数据大小: {len(result.image_data) if result.image_data else 0} bytes")

            if result.success:
                self.logger.info("✅ 统一--again请求执行成功")
                # 更新元数据，标记为again请求
                if result.metadata:
                    result.metadata.update({
                        'is_again_request': True,
                        'original_timestamp': snapshot.timestamp,
                        'again_execution_time': datetime.now().isoformat()
                    })
            else:
                self.logger.error(f"❌ 统一--again请求执行失败: {result.error_message}")
                self.logger.error(f"🔍 [DEBUG] 失败详情 - 工作流类型: {snapshot.workflow_type}, 用户: {request.user_id}")

            return result
            
        except Exception as e:
            self.logger.error(f"❌ 处理统一--again请求异常: {e}")
            import traceback
            self.logger.error(f"🔍 [DEBUG] 异常详情: {traceback.format_exc()}")
            return WorkflowResult(
                success=False,
                error_message=f"重新生成失败: {str(e)}"
            )
    
    def _validate_workflow_type(self, snapshot: WorkflowSnapshot, requested_type: str) -> bool:
        """验证工作流类型是否匹配"""
        
        # 提取快照中的工作流类型
        snapshot_type = self._extract_workflow_type_from_snapshot(snapshot)
        
        # 类型映射和兼容性检查
        type_mapping = {
            'aigen': ['flux_default.json', 'flux_controlnet.json', 'flux_redux.json', 'flux_controlnet_redux.json'],
            'kontext': ['kontext_local_single_image.json', 'kontext_local_double_images.json', 'kontext_local_triple_images.json'],
            'kontext_api': ['kontext_api_single.json', 'kontext_api_double.json', 'kontext_api_triple.json']
        }
        
        # 检查请求的类型是否与快照类型兼容
        if requested_type in type_mapping:
            compatible_files = type_mapping[requested_type]
            return snapshot.workflow_type in compatible_files or snapshot_type == requested_type
        
        return False
    
    def _extract_workflow_type_from_snapshot(self, snapshot: WorkflowSnapshot) -> str:
        """从快照中提取工作流类型"""
        
        workflow_file = snapshot.workflow_type.lower()
        
        if 'flux' in workflow_file or 'aigen' in workflow_file:
            return 'aigen'
        elif 'kontext_api' in workflow_file:
            return 'kontext_api'
        elif 'kontext' in workflow_file:
            return 'kontext'
        else:
            return 'unknown'
    
    async def _execute_workflow_by_type(self, snapshot: WorkflowSnapshot, request: AgainRequest) -> WorkflowResult:
        """根据工作流类型执行相应的工作流"""
        
        workflow_type = self._extract_workflow_type_from_snapshot(snapshot)
        
        if workflow_type == 'aigen':
            return await self._execute_flux_workflow(snapshot)
        elif workflow_type == 'kontext':
            return await self._execute_kontext_workflow(snapshot)
        elif workflow_type == 'kontext_api':
            return await self._execute_kontext_api_workflow(snapshot)
        else:
            return WorkflowResult(
                success=False,
                error_message=f"不支持的工作流类型: {workflow_type}"
            )
    
    async def _execute_flux_workflow(self, snapshot: WorkflowSnapshot) -> WorkflowResult:
        """执行Flux工作流"""
        try:
            # 🔥 修复：直接使用FluxWorkflowManager的ComfyUI执行方法
            from ..flux.flux_workflow_manager import FluxWorkflowManager

            # 创建临时的FluxWorkflowManager实例（仅用于执行ComfyUI）
            temp_manager = FluxWorkflowManager()

            # 🔥 修复：更新seed重新生成，而不是返回相同图片
            self.logger.info(f"🔄 重新生成图片 - 使用相同参数但更新seed")
            self.logger.info(f"📝 原始提示词: {snapshot.user_prompt}")
            self.logger.info(f"🎨 优化提示词: {snapshot.optimized_prompt}")

            # 🔥 关键修复：更新工作流中的seed为随机值
            updated_workflow_data = self._update_workflow_seed(snapshot.workflow_data)

            # 执行更新后的工作流
            image_data = await temp_manager._execute_comfyui_workflow(updated_workflow_data)

            if image_data:
                return WorkflowResult(
                    success=True,
                    image_data=image_data,
                    metadata={
                        'workflow_type': snapshot.workflow_type,
                        'user_prompt': snapshot.user_prompt,
                        'optimized_prompt': snapshot.optimized_prompt,
                        'lora_info': snapshot.lora_info,
                        'parameters': snapshot.parameters,
                        'is_again_request': True
                    }
                )
            else:
                return WorkflowResult(
                    success=False,
                    error_message="ComfyUI工作流执行失败"
                )
                
        except Exception as e:
            self.logger.error(f"执行Flux工作流异常: {e}")
            return WorkflowResult(
                success=False,
                error_message=f"Flux工作流执行异常: {str(e)}"
            )
    
    async def _execute_kontext_workflow(self, snapshot: WorkflowSnapshot) -> WorkflowResult:
        """执行Kontext本地工作流"""
        try:
            self.logger.info(f"🔍 [KONTEXT-AGAIN] 开始执行Kontext工作流 - 类型: {snapshot.workflow_type}")

            # 🔥 修复：直接使用LocalKontextExecutor执行保存的工作流数据
            # 这样可以避免复杂的依赖关系，直接复用底层执行逻辑

            # 🔥 修复：从snapshot中提取实际参数
            self.logger.info(f"🔍 [KONTEXT-AGAIN] 快照参数: {snapshot.parameters}")
            self.logger.info(f"🔍 [KONTEXT-AGAIN] 图片信息: {snapshot.image_info}")
            self.logger.info(f"🔍 [KONTEXT-AGAIN] 工作流数据节点数: {len(snapshot.workflow_data) if snapshot.workflow_data else 0}")

            # 🔥 修复：参考Flux工作流的实现，创建临时的Kontext管理器
            # 并使用类似的_execute_comfyui_workflow方法
            from ..kontext.local_kontext_workflow_manager import LocalKontextWorkflowManager

            # 创建临时的LocalKontextWorkflowManager实例（仅用于执行ComfyUI）
            temp_manager = LocalKontextWorkflowManager()

            self.logger.info(f"🔍 [KONTEXT-AGAIN] 开始执行保存的工作流数据")
            self.logger.info(f"📝 原始提示词: {snapshot.user_prompt}")
            self.logger.info(f"🎨 优化提示词: {snapshot.optimized_prompt}")

            # 🔥 关键修复：直接执行保存的workflow_data，类似Flux的实现
            try:
                # 🔥 增强调试：检查executor状态
                self.logger.info(f"🔍 [KONTEXT-AGAIN] 执行器状态检查:")
                self.logger.info(f"  - 管理器类型: {type(temp_manager)}")
                self.logger.info(f"  - 执行器类型: {type(temp_manager.executor)}")
                self.logger.info(f"  - 执行器API URL: {getattr(temp_manager.executor, 'api_url', 'N/A')}")

                # 检查execute_workflow_data方法是否存在
                if not hasattr(temp_manager.executor, 'execute_workflow_data'):
                    self.logger.error(f"❌ 执行器缺少execute_workflow_data方法")
                    raise AttributeError("执行器缺少execute_workflow_data方法")

                self.logger.info(f"🔍 [KONTEXT-AGAIN] 开始调用execute_workflow_data")

                # 直接调用executor的执行方法
                result = await temp_manager.executor.execute_workflow_data(snapshot.workflow_data)

                self.logger.info(f"🔍 [KONTEXT-AGAIN] execute_workflow_data返回:")
                self.logger.info(f"  - 结果类型: {type(result)}")
                self.logger.info(f"  - 结果对象: {result}")

                if result and hasattr(result, 'success') and result.success:
                    self.logger.info(f"🔍 [KONTEXT-AGAIN] 工作流执行成功，图片大小: {len(result.image_data) if result.image_data else 0} bytes")
                else:
                    error_msg = getattr(result, 'error_message', '未知错误') if result else '执行结果为空'
                    self.logger.error(f"❌ Kontext工作流执行失败: {error_msg}")
                    self.logger.error(f"🔍 [KONTEXT-AGAIN] 失败详情 - 结果: {result}")

            except Exception as exec_error:
                self.logger.error(f"❌ 执行Kontext工作流异常: {exec_error}")
                import traceback
                self.logger.error(f"🔍 [KONTEXT-AGAIN] 异常详情: {traceback.format_exc()}")
                result = type('Result', (), {
                    'success': False,
                    'error_message': str(exec_error)
                })()
            finally:
                # 清理资源
                if hasattr(temp_manager, 'executor'):
                    await temp_manager.executor.close()

            if result and hasattr(result, 'success') and result.success:
                # 构建标准的WorkflowResult
                workflow_result = WorkflowResult(
                    success=True,
                    image_data=getattr(result, 'image_data', None),
                    metadata={
                        'user_prompt': snapshot.user_prompt,
                        'optimized_prompt': snapshot.optimized_prompt,
                        'workflow_type': snapshot.workflow_type,
                        'execution_time': getattr(result, 'execution_time', 0),
                        'is_again': True
                    }
                )

                self.logger.info("✅ Kontext --again 工作流执行成功")
                return workflow_result
            else:
                error_msg = getattr(result, 'error_message', '未知错误') if result else '执行结果为空'
                self.logger.error(f"❌ Kontext工作流执行失败: {error_msg}")
                return WorkflowResult(
                    success=False,
                    error_message=f"Kontext工作流执行失败: {error_msg}"
                )

        except Exception as e:
            self.logger.error(f"❌ 执行Kontext工作流异常: {e}")
            import traceback
            self.logger.error(f"🔍 [KONTEXT-AGAIN] 异常详情: {traceback.format_exc()}")
            return WorkflowResult(
                success=False,
                error_message=f"Kontext工作流执行异常: {str(e)}"
            )
    
    async def _execute_kontext_api_workflow(self, snapshot: WorkflowSnapshot) -> WorkflowResult:
        """执行Kontext API工作流"""
        try:
            from ..kontext_api.kontext_api_workflow_manager import KontextAPIManager
            
            # 创建Kontext API管理器
            # 注意：这里需要传入正确的API密钥和认证令牌
            api_manager = KontextAPIManager("", "")  # 临时实现
            
            # 执行工作流
            result = await api_manager.submit_workflow(
                snapshot.workflow_data,
                snapshot.image_info.get('images', []) if snapshot.image_info else []
            )
            
            if result.success:
                # 更新元数据
                result.metadata.update({
                    'user_prompt': snapshot.user_prompt,
                    'optimized_prompt': snapshot.optimized_prompt,
                    'workflow_type': snapshot.workflow_type
                })
            
            return result
            
        except Exception as e:
            self.logger.error(f"执行Kontext API工作流异常: {e}")
            return WorkflowResult(
                success=False,
                error_message=f"Kontext API工作流执行异常: {str(e)}"
            )
    
    def save_successful_workflow(self,
                                workflow_data: Dict[str, Any],
                                user_prompt: str,
                                optimized_prompt: str,
                                workflow_type: str,
                                parameters: Dict[str, Any],
                                lora_info: Dict[str, Any],
                                image_info: Dict[str, Any],
                                user_id: str,
                                chat_id: str = "") -> bool:
        """
        保存成功的工作流数据

        这是一个统一的保存接口，所有工作流管理器都可以调用
        """
        try:
            self.logger.info(f"🔍 [DEBUG] 保存工作流数据 - 用户: {user_id}, 类型: {workflow_type}")

            # 🔥 修复：添加persistence_manager状态检查
            self.logger.info(f"🔍 [DEBUG] persistence_manager类型: {type(self.persistence_manager)}")
            self.logger.info(f"🔍 [DEBUG] persistence_manager存储目录: {self.persistence_manager.storage_dir}")
            self.logger.info(f"🔍 [DEBUG] 存储目录是否存在: {self.persistence_manager.storage_dir.exists()}")

            result = self.persistence_manager.save_successful_workflow(
                workflow_data=workflow_data,
                user_prompt=user_prompt,
                optimized_prompt=optimized_prompt,
                workflow_type=workflow_type,
                parameters=parameters,
                lora_info=lora_info,
                image_info=image_info,
                user_id=user_id,
                chat_id=chat_id
            )
            if result:
                self.logger.info(f"✅ [DEBUG] 工作流数据保存成功 - 用户: {user_id}")
            else:
                self.logger.warning(f"⚠️ [DEBUG] 工作流数据保存失败 - 用户: {user_id}")
            return result
        except Exception as e:
            self.logger.error(f"❌ 保存工作流数据异常: {e}")
            import traceback
            self.logger.error(f"🔍 [DEBUG] 保存异常详情: {traceback.format_exc()}")
            return False

    def debug_workflow_data(self, user_id: str, chat_id: str = "") -> Dict[str, Any]:
        """调试工作流数据状态"""
        try:
            self.logger.info(f"🔍 [DEBUG] 检查工作流数据状态 - 用户: {user_id}")

            # 检查是否有保存的数据
            snapshot = self.persistence_manager.load_last_successful_workflow(user_id, chat_id)

            if snapshot:
                return {
                    'has_data': True,
                    'workflow_type': snapshot.workflow_type,
                    'timestamp': snapshot.timestamp,
                    'user_prompt': snapshot.user_prompt[:50] + '...' if len(snapshot.user_prompt) > 50 else snapshot.user_prompt,
                    'optimized_prompt': snapshot.optimized_prompt[:50] + '...' if len(snapshot.optimized_prompt) > 50 else snapshot.optimized_prompt
                }
            else:
                return {
                    'has_data': False,
                    'message': '没有找到工作流数据'
                }
        except Exception as e:
            self.logger.error(f"调试工作流数据失败: {e}")
            return {
                'has_data': False,
                'error': str(e)
            }

    def _update_workflow_seed(self, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新工作流中的seed为随机值，确保每次--again都生成不同的图片

        Args:
            workflow_data: 原始工作流数据

        Returns:
            更新seed后的工作流数据
        """
        try:
            # 深拷贝工作流数据，避免修改原始数据
            updated_workflow = copy.deepcopy(workflow_data)

            # 生成新的随机seed
            new_seed = random.randint(1, 2**32 - 1)
            self.logger.info(f"🎲 生成新的随机seed: {new_seed}")

            # 查找并更新所有可能的seed节点
            seed_updated = False
            for node_id, node_data in updated_workflow.items():
                if isinstance(node_data, dict) and 'inputs' in node_data:
                    inputs = node_data['inputs']

                    # 检查常见的seed字段名
                    seed_fields = ['seed', 'noise_seed', 'random_seed']
                    for field in seed_fields:
                        if field in inputs:
                            old_seed = inputs[field]
                            inputs[field] = new_seed
                            self.logger.info(f"🔄 节点 {node_id}: 更新 {field} 从 {old_seed} 到 {new_seed}")
                            seed_updated = True

                    # 特殊处理：检查是否是KSampler类型的节点
                    class_type = node_data.get('class_type', '')
                    if 'sampler' in class_type.lower() or 'ksampler' in class_type.lower():
                        if 'seed' in inputs:
                            old_seed = inputs['seed']
                            inputs['seed'] = new_seed
                            self.logger.info(f"🎯 采样器节点 {node_id} ({class_type}): 更新seed从 {old_seed} 到 {new_seed}")
                            seed_updated = True

                    # 🔥 新增：Kontext专用节点处理
                    if class_type == "FluxKontextProImageNode":
                        if 'seed' in inputs:
                            old_seed = inputs['seed']
                            inputs['seed'] = new_seed
                            self.logger.info(f"🎯 Kontext节点 {node_id} ({class_type}): 更新seed从 {old_seed} 到 {new_seed}")
                            seed_updated = True

            if not seed_updated:
                self.logger.warning("⚠️ 未找到seed字段，可能无法生成不同的图片")
            else:
                self.logger.info(f"✅ 成功更新工作流seed，将生成新的图片")

            return updated_workflow

        except Exception as e:
            self.logger.error(f"❌ 更新工作流seed失败: {e}")
            # 如果更新失败，返回原始数据
            return workflow_data


# 全局单例
unified_again_manager = UnifiedAgainManager()
